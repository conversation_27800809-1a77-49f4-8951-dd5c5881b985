import { CreateTargetCommentDto } from 'src/packages/proposal-comments/dtos/create-comment.dto';
import { validate } from 'class-validator';

type ValidationError = import('class-validator').ValidationError;

describe('CreateTargetCommentDto', () => {
  it('should be defined', () => {
    expect(new CreateTargetCommentDto()).toBeDefined();
  });

  it('should fail validation when comment is missing', async () => {
    const dto = new CreateTargetCommentDto();

    const errors: ValidationError[] = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    // find error for 'comment' property
    const commentError = errors.find((e) => e.property === 'comment');
    expect(commentError).toBeDefined();
  });

  it('should fail validation when comment is not a string', async () => {
    const dto: any = new CreateTargetCommentDto();
    dto.comment = 123;

    const errors: ValidationError[] = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    const commentError = errors.find((e) => e.property === 'comment');
    expect(commentError).toBeDefined();
  });

  it('should pass validation with a valid comment', async () => {
    const dto = new CreateTargetCommentDto();
    dto.comment = 'This is a valid comment';
    dto.createdBy = '6861F84A-AC95-4C1A-B30D-0D8A703B719C';

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });
});
