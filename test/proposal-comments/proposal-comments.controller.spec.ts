import { Test, TestingModule } from '@nestjs/testing';
import { ProposalCommentsController } from 'src/packages/proposal-comments/proposal-comments.controller';
import { ProposalCommentsService } from 'src/packages/proposal-comments/proposal-comments.service';
import { CreateTargetCommentDto } from 'src/packages/proposal-comments/dtos';
import { ProposalComments } from '@ghq-abi/northstar-domain';

describe('ProposalCommentsController', () => {
  let controller: ProposalCommentsController;
  let service: ProposalCommentsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProposalCommentsController],
      providers: [
        {
          provide: ProposalCommentsService,
          useValue: {
            getProposalComments: jest.fn(),
            createProposalComment: jest.fn(),
            deleteProposalComment: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<ProposalCommentsController>(
      ProposalCommentsController,
    );
    service = module.get<ProposalCommentsService>(ProposalCommentsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getProposalComments', () => {
    it('should call service.getProposalComments with correct params and return result', async () => {
      const proposalId = 'proposal-1';
      const pageNumber = 2;
      const pageSize = 5;
      const mockResult = {
        data: [{ id: '1' } as ProposalComments],
        pageNumber,
        pageSize,
        totalRecords: 1,
      };
      (service.getProposalComments as jest.Mock).mockResolvedValue(mockResult);

      const result = await controller.getProposalComments(
        proposalId,
        pageNumber,
        pageSize,
      );

      expect(service.getProposalComments).toHaveBeenCalledWith(
        proposalId,
        pageNumber,
        pageSize,
      );
      expect(result).toBe(mockResult);
    });

    it('should use default pageNumber and pageSize if not provided', async () => {
      const proposalId = 'proposal-2';
      const mockResult = {
        data: [],
        pageNumber: 1,
        pageSize: 10,
        totalRecords: 0,
      };
      (service.getProposalComments as jest.Mock).mockResolvedValue(mockResult);

      const result = await controller.getProposalComments(
        proposalId,
        undefined as any,
        undefined as any,
      );

      expect(service.getProposalComments).toHaveBeenCalledWith(
        proposalId,
        1,
        10,
      );
      expect(result).toBe(mockResult);
    });
  });

  describe('createProposalComment', () => {
    it('should call service.createProposalComment with correct params and return result', async () => {
      const proposalId = 'proposal-3';
      const employeeId = '6861F84A-AC95-4C1A-B30D-0D8A703B719C';
      const body: CreateTargetCommentDto = {
        comment: 'Test comment',
        createdBy: employeeId,
      };
      const mockComment = {
        id: 'c1',
        comment: body.comment,
        author: { globalId: 'author-1', name: 'Test Author' },
        message: body.comment,
        createdAt: new Date(),
      } as ProposalComments;
      (service.createProposalComment as jest.Mock).mockResolvedValue(
        mockComment,
      );

      const result = await controller.createProposalComment(proposalId, body);

      expect(service.createProposalComment).toHaveBeenCalledWith(
        proposalId,
        body.comment,
        employeeId,
      );
      expect(result).toBe(mockComment);
    });
  });

  describe('deleteProposalComment', () => {
    it('should call service.deleteProposalComment with correct messageId', async () => {
      const messageId = 'msg-1';
      const mockResult = { success: true };
      (service.deleteProposalComment as jest.Mock).mockResolvedValue(
        mockResult,
      );

      const result = await controller.deleteProposalComment(messageId);

      expect(service.deleteProposalComment).toHaveBeenCalledWith(messageId);
      expect(result).toBe(mockResult);
    });
  });
});
