import { Test, TestingModule } from '@nestjs/testing';
import { ProposalCommentsService } from 'src/packages/proposal-comments/proposal-comments.service';
import {
  ProposalCommentsRepository,
  ProposalComments,
} from '@ghq-abi/northstar-domain';

describe('ProposalCommentsService', () => {
  let service: ProposalCommentsService;

  const mockRepo = {
    findMessagesWithPagination: jest.fn().mockResolvedValue({
      data: [] as ProposalComments[],
      pageNumber: 1,
      pageSize: 10,
      totalRecords: 0,
    }),
    createMessage: jest.fn().mockResolvedValue({} as ProposalComments),
    deleteMessage: jest.fn().mockResolvedValue(true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProposalCommentsService,
        { provide: ProposalCommentsRepository, useValue: mockRepo },
      ],
    }).compile();

    service = module.get<ProposalCommentsService>(ProposalCommentsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('getProposalComments calls repository and returns pagination', async () => {
    const res = await service.getProposalComments('proposal-1', 1, 10);
    expect(mockRepo.findMessagesWithPagination).toHaveBeenCalledWith(
      'proposal-1',
      1,
      10,
    );
    expect(res).toHaveProperty('data');
    expect(res.pageNumber).toBe(1);
  });

  it('createProposalComment calls repository', async () => {
    await service.createProposalComment(
      'proposal-1',
      'a comment',
      'employee-1',
    );
    expect(mockRepo.createMessage).toHaveBeenCalledWith(
      'proposal-1',
      'a comment',
      'employee-1',
    );
  });

  it('deleteProposalComment calls repository', async () => {
    await service.deleteProposalComment('message-1');
    expect(mockRepo.deleteMessage).toHaveBeenCalledWith('message-1');
  });

  it('createProposalComment throws NotFoundException if repository throws NotFoundException', async () => {
    const error = { name: 'NotFoundException', message: 'Not found' };
    mockRepo.createMessage.mockRejectedValueOnce(error);

    await expect(
      service.createProposalComment('proposal-1', 'a comment', 'employee-1'),
    ).rejects.toThrowError('Not found');
    mockRepo.createMessage.mockResolvedValue({} as ProposalComments); // reset mock
  });

  it('createProposalComment rethrows unknown errors', async () => {
    const error = { name: 'OtherError', message: 'Some error' };
    mockRepo.createMessage.mockRejectedValueOnce(error);

    await expect(
      service.createProposalComment('proposal-1', 'a comment', 'employee-1'),
    ).rejects.toEqual(error);
    mockRepo.createMessage.mockResolvedValue({} as ProposalComments); // reset mock
  });
});
