# Introduction
Catchball API, everything related to managing the KPIs.

# Getting Started
1.	Installation process: yarn
2.	Software dependencies: tiny-csrf, express, express-session, mssql, nestjs, typeorm, helmet


# Build and Test
yarn start:dev

# Contribute

Please remember to use the prettier, the linter, to create pull requests and ask someone to review it.
Also, when creating migrations, remember to remove the "ALTER TABLE ... DROP COLUMN startTime/endTime" that are created. Typeorm does not support temporal tables yet.

# 🛠 TypeORM Migration Guide

1. Creates a new empty migration file with a custom name.

```bash
yarn typeorm migration:create src/migrations/KpiCatalog
```

2. Generate a Migration from Entity Changes (change de name also)

```bash
yarn typeorm migration:generate src/migrations/KpiCatalog -d ./src/configs/datasource.ts
```

3. Run All Pending Migrations

```bash
yarn typeorm migration:run -d ./src/configs/datasource.ts
```

4. Revert the Last Migration
```bash
yarn typeorm migration:revert -d ./src/configs/datasource.ts
```

# Database Structure (Mermaid ER Diagram)

The following diagram illustrates the entity-relationship model used by the Catchball database. It is written using Mermaid syntax, which allows visualizing database models directly in Markdown-compatible tools.

https://mermaid.live/edit#pako:eNrdVUuP2jAQ_ivRnAMKCc9cu9rLqhKHnioka4iH4K5jZx0blbL89zqEQNhkpb22PtnzPcYej-wTZJoTpEDmSWBusNiowI8nkuJABreSfhxLCt7fRyN96oaDNNhj1bDXRv-izLas65J12JWnS6Feq57910TDuzo14XpU1giVB_VpgvVLEz8P6AY0TvCbpBNWWFAvuHMqs0KrPmDozZHKjndkq7UkVIGoGHrRoe9WaWeyTlgoG7gK8z4zQ5k5iXVqVpDda96jcNoJJQY3VyI7oHR9261jkg4kGea5oRwH1b4-jN8ryGxd-ud-wTJDaImz7bFvUfLPIO9MH6GabEVBN0u0A2BrOgi2tl3wmlEbkQv1oUXaFv4320Nv680_eNwr4SessmjsZyAp_l9e5uNbMvxYsPJ68euXcLCpa06n-x94PiGEkBvBIbXGUQgFmQLrJVzSbcDuyfcJpH7K0bxuYKPOXlOi-ql10cqMdvke0h3Kyq-aWlzf4xvF3xKZb9opC-kkulhAeoLfkMbTZLxKptFkGi-i-WwZJSEcIR3N5-NlMolmy4Ufyzg-h_DnkjQar1aLKEmSaBbPVlGymIRAXFhtvje_weVTOP8F2GHvPg