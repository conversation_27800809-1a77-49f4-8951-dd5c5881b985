trigger:
  branches:
    include:
      - dev
      - staging
      - main

resources:
  repositories:
    - repository: appsectemplates
      type: git
      name: DevSecOps/DevSecOps
      ref: 'orcaContainerScan'

##################
# Radio Button
##################
parameters:
  - name: selectedEnv
    displayName: Select environment
    type: string
    default: None
    values:
      - None
      - 'tsc-dev'
      - 'tsc-qa'
      - 'tsc-prod'

##################
# Azure DevOps > Pipelines > Library > Variable Groups
##################
variables:
    - group: "build-resources"
    - ${{ if and(eq(variables['Build.Reason'], 'Manual'), ne(parameters.selectedEnv, 'None')) }}:
      - ${{ if eq(parameters.selectedEnv, 'tsc-prod') }}:
        - group: "peopleplatform-deploy-production"
        - group: tsc-prod
      - ${{ elseif eq(parameters.selectedEnv, 'tsc-qa') }}:
        - group: "peopleplatform-deploy-staging"
        - group: tsc-qa
      - ${{ else }}:
        - group: "peopleplatform-deploy-development"
        - group: tsc-dev
    - ${{ elseif eq(variables['Build.SourceBranch'], 'refs/heads/dev') }}:
        - group: "peopleplatform-deploy-development"
        - group: tsc-dev
    - ${{ elseif eq(variables['Build.SourceBranch'], 'refs/heads/staging') }}:
        - group: "peopleplatform-deploy-staging"
        - group: tsc-qa
    - ${{ elseif eq(variables['Build.SourceBranch'], 'refs/heads/main') }}:
        - group: "peopleplatform-deploy-production"
        - group: tsc-prod

    - name: scanTemplate
      ${{ if eq(variables['Build.Reason'], 'PullRequest') }}:
        value: 'pipeline_templates/Security_tasks/prepareSonarcloudPR.yml@appsectemplates'
      ${{ else }}:
        value: 'pipeline_templates/Security_tasks/prepareSonarCloud.yml@appsectemplates'

    - name: tag
      value: $(Build.BuildId)
    - name: IMAGE_DIGEST
      value: ''
    - name: appsec-akv-sp-clientid
      value: aa52a4de-800c-499d-bb36-7cef858a1d3b

stages:

- stage: Build
  displayName: Build and secure code scan
  jobs:
    - job: Build
      displayName: Build
      pool:
        vmImage: $(vmImageName)

      steps:
        - checkout: self
        - checkout: appsectemplates

        - task: NodeTool@0
          inputs:
            versionSpec: '22.x'
          displayName: 'Install Node.js'

        # install dependecies
        - task: Bash@3
          inputs:
            targetType: 'inline'
            script: |
              echo "registry=https://pkgs.dev.azure.com/ab-inbev/030af4ce-9b3e-405c-8466-18a00cfc08ac/_packaging/PeopleTech.NorthstarDomain/npm/registry/" >> $(Build.Repository.Name)/.npmrc
              echo "always-auth=true" >> $(Build.Repository.Name)/.npmrc

        - task: npmAuthenticate@0
          inputs:
            workingFile: '$(Build.Repository.Name)/.npmrc'

        - script: yarn
          displayName: 'Yarn install'
          workingDirectory: $(Build.Repository.Name)

        - task: PublishCodeCoverageResults@2
          inputs:
            summaryFileLocation: '$(Build.Repository.Name)/coverage/lcov.info'

        - template: ${{ variables['scanTemplate'] }}
          parameters:
            SCServiceConnection: "SonarcloudServer"
            SCProjectKey: "GHQ_ABI_PEOPLE_TECH_PLATFORM_GHQ_ABI_NORTHSTAR_CATCHBALL_API"
            SCProjectName: "GHQ_ABI_PEOPLE_TECH_PLATFORM_GHQ_ABI_NORTHSTAR_CATCHBALL_API"
            SCBaseDirPath: "./$(Build.Repository.Name)"
            SCSourceEncoding: "UTF-8"
            SCReportsPathType: sonar.javascript.lcov.reportPaths
            SCReportsPath: ./coverage/lcov.info
            SCExclusion: '**/node_modules/**,**/test/**,**/tests/**,**/coverage/**,**/dist/**,**/build/**,**/*.spec.ts'
            scanOrca: true
            OrcaImage: peopleproductsacr.azurecr.io/tsc/catchball-api:$(tag)
            OrcaProjectKey: 'GHQ_PeopleTech'
            ${{ if or(eq(variables['Build.Reason'], 'IndividualCI'), eq(variables['Build.Reason'], 'Manual'))}}:
              SCBranchName: "$(Build.SourceBranchName)"
            ${{ if eq(variables['Build.Reason'], 'PullRequest') }}:
              SCPRKey: $(System.PullRequest.PullRequestId)
              SCPrBranch: $(System.PullRequest.SourceBranch)
              SCPrBaseBranch: $(System.PullRequest.TargetBranch)

        - script: |
            yarn build
            yarn test:cov --passWithNoTests
          displayName: 'Yarn build and test'
          workingDirectory: $(Build.Repository.Name)

        - task: Docker@2
          displayName: 'Docker build and push'
          inputs:
              containerRegistry: 'AzurePeopleProducts'
              repository: tsc/catchball-api
              command: 'buildAndPush'
              Dockerfile: '**/Dockerfile'
              tags: |
                $(Build.BuildId)
                latest

        - task: AzureCLI@2
          displayName: 'Sign and Verify Image'
          inputs:
              azureSubscription: $(subscription)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |

                curl -sL https://github.com/sigstore/cosign/releases/latest/download/cosign-linux-amd64 --output /usr/local/bin/cosign
                chmod +x /usr/local/bin/cosign

                # Login and get digest
                az acr login -n peopleproductsacr.azurecr.io
                IMAGE_DIGEST=$(az acr repository show --name peopleproductsacr.azurecr.io --image tsc/catchball-api:$(tag) --query digest --output tsv)

                # Sign image
                cosign sign --key azurekms://$(AKV_URI)/$(SigningKeyName) \
                  --tlog-upload=false \
                  peopleproductsacr.azurecr.io/tsc/catchball-api@$IMAGE_DIGEST

                # Verify signature
                cosign verify --key azurekms://$(AKV_URI)/$(SigningKeyName) \
                  --insecure-ignore-tlog \
                  peopleproductsacr.azurecr.io/tsc/catchball-api:$(tag)
          env:
            AZURE_TENANT_ID: cef04b19-7776-4a94-b89b-375c77a8f936
            AZURE_CLIENT_ID: $(appsec-akv-sp-clientid)
            AZURE_CLIENT_SECRET: $(appsec-akv-sp-password)

        - template: pipeline_templates/secure_code_scan.yml@appsectemplates
          parameters:
            scanSonarCloud: true
            sonarCloudGate: false
            SCServiceConnection: "SonarcloudServer"
            SCOrganization: "sonarcloud-ado"
            scanOrca: true
            OrcaImage: 'peopleproductsacr.azurecr.io/tsc/catchball-api:$(Build.BuildId)'
            OrcaProjectKey: 'GHQ_PeopleTech'
            scanSnyk: true
            SKFailOnIssues: true
            SkServiceConnection: "SnykServer"
            SkOrganization: "************************************"
            SkAdditionalArgs: "--all-projects --prune-repeated-subdependencies --detection-depth=4"
            App360ID: "SE-10070"
            scanApiiro: true
            AprServiceConnection: "apiiro"
            AprSkipOnScanFailure: false

        - task: PublishPipelineArtifact@1
          displayName: 'Publish Manifests'
          inputs:
            artifactName: 'manifests'
            targetPath: $(Build.SourcesDirectory)

- stage: Deployment
  displayName: 'Deploy to Environment'
  dependsOn: Build
  condition: succeeded()
  jobs:
    - deployment:  deployment
      displayName: Select environment
      environment: $(environmentName)

    - job: Deploy
      displayName: Deploy
      steps:
        - task: replacetokens@6
          inputs:
            sources: 'pipelines/manifests/*.yml'
            tokenPattern: 'custom'
            tokenPrefix: '${'
            tokenSuffix: '}'
            addBOM: true
            missingVarAction: 'keep'
            missingVarLog: 'error'
            ifNoFilesFound: 'error'

        - task: Bash@3
          inputs:
            targetType: 'inline'
            script: |
              cat pipelines/manifests/deployment.yml

        - task: KubernetesManifest@1
          displayName: Deploy
          inputs:
              action: deploy
              connectionType: 'kubernetesServiceConnection'
              kubernetesServiceConnection: $(k8srvcon)
              namespace: $(k8sNamespace)
              imagePullSecrets: $(imagePullSecrets)
              manifests: |
                pipelines/manifests/*.yml
              containers: |
                  peopleproductsacr.azurecr.io/tsc/catchball-api:$(tag)

        - task: Kubernetes@1
          displayName: image update
          inputs:
              connectionType: 'Kubernetes Service Connection'
              kubernetesServiceEndpoint: $(k8srvcon)
              namespace: $(k8sNamespace)
              command: 'set'
              arguments: 'image deployment/tsc-catchball-api tsc-catchball-api=peopleproductsacr.azurecr.io/tsc/catchball-api:$(tag)'
              secretType: 'dockerRegistry'
              containerRegistryType: 'Azure Container Registry'
