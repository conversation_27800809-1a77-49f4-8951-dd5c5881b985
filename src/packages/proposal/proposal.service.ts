import {
  ProposalRepository,
  ProposalEntity,
  FindProposalsFilters,
  TargetRepository,
  TargetTypeRepository,
  TargetEntity,
  TargetTypeEntity,
} from '@ghq-abi/northstar-domain';
import { Injectable } from '@nestjs/common';
import { CreateTargetsOnProposalDto } from './dtos/create-targets-on-proposal.dto';

@Injectable()
export class ProposalService {
  constructor(
    private readonly proposalRepository: ProposalRepository,
    private readonly targetRepository: TargetRepository,
    private readonly targetTypeRepository: TargetTypeRepository,
  ) {}

  public async findAllProposals(
    filters: FindProposalsFilters,
    pageNumber: number,
    pageSize: number,
  ): Promise<{
    data: ProposalEntity[];
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  }> {
    return this.proposalRepository.findAllProposals(
      filters,
      pageNumber,
      pageSize,
    );
  }

  public async createTargetsOnProposal(
    uidProposal: string,
    createTargetsOnProposalDto: CreateTargetsOnProposalDto,
  ): Promise<ProposalEntity> {
    await Promise.all(
      createTargetsOnProposalDto.targets.map(async (target) => {
        let parentWeight = target.weight;
        if (target.children && target.children.length > 0) {
          parentWeight = target.children.reduce(
            (sum, child) => sum + (child.weight || 0),
            0,
          );
        }

        const createdTarget = await this.targetRepository.createTarget({
          scope: target.scope,
          weight: parentWeight ?? target.weight,
          uidProposal: uidProposal,
          uidDeliverable: target.uidDeliverable,
        } as unknown as TargetEntity);

        if (target.children && target.children.length > 0) {
          const childrenToCreate = target.children.filter(
            (child) => !child.uid,
          );
          const childrenToUpdate = target.children.filter((child) => child.uid);

          if (childrenToCreate.length > 0) {
            await this.targetRepository.createTargets(
              childrenToCreate.map(
                (child) =>
                  ({
                    ...child,
                    uidParentTarget: createdTarget.uid,
                  }) as unknown as TargetEntity,
              ),
            );
          }

          if (childrenToUpdate.length > 0) {
            await this.targetRepository.createTargets(
              childrenToUpdate.map(
                (child) =>
                  ({
                    ...child,
                    uidParentTarget: createdTarget.uid,
                  }) as unknown as TargetEntity,
              ),
            );
          }
        }

        if (target.targetType) {
          await this.targetTypeRepository.create({
            uidTarget: createdTarget.uid,
            type: target.targetType,
          } as unknown as TargetTypeEntity);
        }

        return createdTarget;
      }),
    );

    return this.proposalRepository.findByUid(uidProposal);
  }

  public async findProposalByUid(uid: string): Promise<ProposalEntity | null> {
    return this.proposalRepository.findByUid(uid);
  }

  public async updateProposal(
    uid: string,
    proposalData: Partial<ProposalEntity>,
  ): Promise<ProposalEntity> {
    return this.proposalRepository.updateProposal(uid, proposalData);
  }

  public async deleteProposal(uid: string): Promise<void> {
    return this.proposalRepository.deleteProposal(uid);
  }

  public async deleteTargetsFromProposal(
    uidProposal: string,
    targetsUids: string[],
  ): Promise<ProposalEntity> {
    return await this.proposalRepository.deleteTargetsFromProposalByUids(
      uidProposal,
      targetsUids,
    );
  }

  public async getFilters(): Promise<{
    data: {
      status: { label: string; value: string }[];
      zones: { label: string; value: string }[];
      functions: { label: string; value: string }[];
      sltLevels: { label: string; value: string }[];
      sltNames: { label: string; value: string }[];
    };
  }> {
    return this.proposalRepository.getFilters();
  }

  public async updateTargetsFromProposal(
    uidProposal: string,
    updateTargetsOnProposalDto: CreateTargetsOnProposalDto,
  ): Promise<ProposalEntity> {
    await Promise.all(
      updateTargetsOnProposalDto.targets.map(async (target) => {
        const updatedTarget = await this.targetRepository.updateTarget({
          uid: target.uid,
          scope: target.scope,
          weight: target.weight,
        } as unknown as TargetEntity);

        if (target.children && target.children.length > 0) {
          await Promise.all(
            target.children.map(async (child) =>
              this.targetRepository.updateTarget({
                uid: child.uid,
                scope: child.scope,
                weight: child.weight,
              } as unknown as TargetEntity),
            ),
          );

          const totalChildrenWeight = target.children.reduce(
            (sum, child) => sum + (child.weight || 0),
            0,
          );

          if (totalChildrenWeight !== target.weight) {
            await this.targetRepository.updateTarget({
              uid: target.uid,
              weight: totalChildrenWeight ?? target.weight,
            } as unknown as TargetEntity);
          }
        }

        return updatedTarget;
      }),
    );

    return this.proposalRepository.findByUid(uidProposal);
  }
}
