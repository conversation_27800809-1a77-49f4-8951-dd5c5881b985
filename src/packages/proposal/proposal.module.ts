import { Module } from '@nestjs/common';
import { ProposalService } from './proposal.service';
import { ProposalController } from './proposal.controller';
import {
  EmployeeProfilesEntity,
  ProposalEntity,
  ProposalRepository,
  RoleEntity,
  ZoneEntity,
} from '@ghq-abi/northstar-domain';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TargetsModule } from '../targets/targets.module';
import { TargetTypeModule } from '../target-type/target-type.module';
import { TargetsService } from '../targets/targets.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ProposalEntity,
      EmployeeProfilesEntity,
      RoleEntity,
      ZoneEntity,
    ]),
    TargetsModule,
    TargetTypeModule,
  ],
  controllers: [ProposalController],
  providers: [ProposalService, ProposalRepository, TargetsService],
  exports: [
    TypeOrmModule.forFeature([ProposalEntity]),
    ProposalService,
    ProposalRepository,
  ],
})
export class ProposalModule {}
