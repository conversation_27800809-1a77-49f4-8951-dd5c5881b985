import { ProposalStatus, TargetType } from '@ghq-abi/northstar-domain';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateProposalTargetsDto {
  @ApiProperty({
    description: 'Array of unique identifiers for the parent targets',
    example: ['2A9F71DB-2E2E-41AA-B047-D65EF7813AA4'],
  })
  parentTargetsIds: string[];

  @ApiProperty({
    description: 'Current status of the proposal',
    enum: ProposalStatus,
    example: ProposalStatus.IN_PROGRESS_FEEDBACK,
  })
  proposalStatus: ProposalStatus;

  @ApiProperty({
    description: 'Type of the target',
    enum: TargetType,
    example: TargetType.FEEDBACK,
  })
  targetTypes: TargetType;
}
