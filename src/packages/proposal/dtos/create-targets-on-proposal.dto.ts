import { ApiProperty } from '@nestjs/swagger';
import { CreateTargetDto } from 'src/packages/targets/dtos';

export class CreateTargetsOnProposalDto {
  @ApiProperty({
    description: 'Unique identifier for the proposal',
    example: 'A70BB61B-47DF-4A4B-9525-36118C76AD8A',
    required: false,
  })
  uidProposal?: string;

  @ApiProperty({
    description: 'List of targets to be created',
    type: [CreateTargetDto],
  })
  targets: CreateTargetDto[];
}
