import { ApiProperty } from '@nestjs/swagger';
import { CreateTargetDto } from 'src/packages/targets/dtos';

export class CreateProposalDto {
  @ApiProperty({
    description: 'The status of the proposal',
    example: 'pending',
  })
  status: string;

  @ApiProperty({
    description: 'Unique identifier of the employee who created the proposal',
    example: 'C8CE56CA-683E-45FB-B5AB-7ACCD3470642',
  })
  uidEmployee: string;

  @ApiProperty({
    description: 'Start date of the proposal period in ISO format',
    example: '2025-08-21',
  })
  dateStart: string;

  @ApiProperty({
    description: 'End date of the proposal period in ISO format',
    example: '2025-12-31',
  })
  dateEnd: string;

  @ApiProperty({
    description: 'Array of targets associated with this proposal',
    type: [CreateTargetDto],
  })
  targets: CreateTargetDto[];
}
