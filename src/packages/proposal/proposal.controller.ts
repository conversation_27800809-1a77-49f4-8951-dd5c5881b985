import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ProposalService } from './proposal.service';
import { ApiBody, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { ProposalEntity } from '@ghq-abi/northstar-domain';
import { CreateTargetsOnProposalDto } from './dtos/create-targets-on-proposal.dto';

@ApiTags('Proposals')
@Controller('proposals')
export class ProposalController {
  constructor(private readonly proposalService: ProposalService) {}

  @Get('/filters')
  public async getFilters(): Promise<{
    data: {
      status: { label: string; value: string }[];
      zones: { label: string; value: string }[];
      functions: { label: string; value: string }[];
      sltLevels: { label: string; value: string }[];
      sltNames: { label: string; value: string }[];
    };
  }> {
    return this.proposalService.getFilters();
  }

  @Get(':uid')
  public async getProposal(@Param('uid') uid: string): Promise<ProposalEntity> {
    return this.proposalService.findProposalByUid(uid);
  }

  @Get()
  @ApiQuery({ name: 'pageNumber', required: false, type: Number })
  @ApiQuery({ name: 'pageSize', required: false, type: Number })
  @ApiQuery({
    name: 'employeeIdentifier',
    required: false,
    type: String,
  })
  @ApiQuery({ name: 'status', required: false, type: Array<string> })
  @ApiQuery({ name: 'zones', required: false, type: Array<string> })
  @ApiQuery({ name: 'funcs', required: false, type: Array<string> })
  @ApiQuery({ name: 'sltLevel', required: false, type: Array<string> })
  @ApiQuery({ name: 'sltName', required: false, type: Array<string> })
  public async findAllProposals(
    @Query('employeeIdentifier') employeeIdentifier: string | number,
    @Query('status') status: string[],
    @Query('zones') zones: string[],
    @Query('funcs') funcs: string[],
    @Query('sltLevel') sltLevel: string[],
    @Query('sltName') sltName: string[],
    @Query('pageNumber') pageNumber: number,
    @Query('pageSize') pageSize: number,
  ): Promise<{
    data: ProposalEntity[];
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  }> {
    return this.proposalService.findAllProposals(
      { employeeIdentifier, status, zones, funcs, sltLevel, sltName },
      pageNumber,
      pageSize,
    );
  }

  @Post(':uidProposal/create/targets')
  @ApiParam({ name: 'uidProposal', required: true, type: String })
  public async createTargetsOnProposal(
    @Param('uidProposal') uidProposal: string,
    @Body() createTargetsOnProposalDto: CreateTargetsOnProposalDto,
  ): Promise<ProposalEntity> {
    return this.proposalService.createTargetsOnProposal(
      uidProposal,
      createTargetsOnProposalDto,
    );
  }

  @Put(':uidProposal/status')
  @ApiParam({ name: 'uidProposal', required: true, type: String })
  @ApiQuery({ name: 'status', required: true, type: String })
  public async updateProposalStatus(
    @Param('uidProposal') uidProposal: string,
    @Query('status') status: Partial<ProposalEntity['status']>,
  ): Promise<ProposalEntity> {
    return this.proposalService.updateProposal(uidProposal, { status });
  }

  @Delete(':uidProposal/targets')
  @ApiParam({ name: 'uidProposal', required: true, type: String })
  @ApiBody({
    schema: {
      type: 'object',
      properties: { targets: { type: 'array', items: { type: 'string' } } },
    },
  })
  public async deleteTargetsFromProposal(
    @Param('uidProposal') uidProposal: string,
    @Body('targets') targetsUids: string[],
  ): Promise<ProposalEntity> {
    return this.proposalService.deleteTargetsFromProposal(
      uidProposal,
      targetsUids,
    );
  }

  @Put(':uidProposal/targets')
  @ApiParam({ name: 'uidProposal', required: true, type: String })
  @ApiBody({
    schema: {
      type: 'object',
      properties: { targets: { type: 'array', items: { type: 'object' } } },
    },
  })
  public async updateTargetsFromProposal(
    @Param('uidProposal') uidProposal: string,
    @Body() targets: CreateTargetsOnProposalDto,
  ): Promise<ProposalEntity> {
    return this.proposalService.updateTargetsFromProposal(uidProposal, targets);
  }
}
