import {
  TargetTypeEntity,
  TargetTypeRepository,
} from '@ghq-abi/northstar-domain';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TargetTypeController } from './target-type.controller';
import { TargetTypeService } from './target-type.service';

@Module({
  imports: [TypeOrmModule.forFeature([TargetTypeEntity])],
  providers: [TargetTypeRepository, TargetTypeService],
  exports: [TypeOrmModule.forFeature([TargetTypeEntity]), TargetTypeRepository],
  controllers: [TargetTypeController],
})
export class TargetTypeModule {}
