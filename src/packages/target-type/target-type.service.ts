import {
  TargetType,
  TargetTypeEntity,
  TargetTypeRepository,
} from '@ghq-abi/northstar-domain';
import { Injectable } from '@nestjs/common';

@Injectable()
export class TargetTypeService {
  constructor(private readonly targetTypeRepository: TargetTypeRepository) {}

  public async createTargetType(
    uidTargets: string[],
    type: TargetType,
  ): Promise<TargetTypeEntity[] | null> {
    const existingTargetTypes =
      await this.verifyIfTargetTypesExists(uidTargets);

    const createdEntities = await Promise.all(
      uidTargets.map(async (uidTarget) => {
        const existingTargetType = existingTargetTypes.find(
          (targetType) =>
            targetType.uidTarget === uidTarget && targetType.type === type,
        );

        if (existingTargetType) {
          return existingTargetType;
        }

        const newTargetType: TargetTypeEntity = {
          uidTarget,
          type,
        } as TargetTypeEntity;

        return this.targetTypeRepository.createTargetType(newTargetType);
      }),
    );

    return createdEntities;
  }

  private async verifyIfTargetTypesExists(
    uidTargets: string[],
  ): Promise<TargetTypeEntity[]> {
    return this.targetTypeRepository.findTargetTypesByTargetUids(uidTargets);
  }
}
