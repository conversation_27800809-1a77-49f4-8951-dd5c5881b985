import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { TargetTypeService } from './target-type.service';
import { TargetType } from '@ghq-abi/northstar-domain';

@ApiTags('Target Type')
@Controller('target-type')
export class TargetTypeController {
  constructor(private readonly targetTypeService: TargetTypeService) {}

  @Post('feedback')
  public async createFeedback(@Body() uidTargets: string[]) {
    return this.targetTypeService.createTargetType(
      uidTargets,
      TargetType.FEEDBACK,
    );
  }

  @Post('final')
  public async createFinal(@Body() uidTargets: string[]) {
    return this.targetTypeService.createTargetType(
      uidTargets,
      TargetType.FINAL,
    );
  }
}
