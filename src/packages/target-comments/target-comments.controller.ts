import {
  Body,
  Controller,
  Get,
  Post,
  Param,
  Query,
  Delete,
} from '@nestjs/common';
import { ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { TargetComments } from '@ghq-abi/northstar-domain';
import { CreateTargetCommentDto } from './dtos/create-comment.dto';
import { TargetCommentsService } from './target-comments.service';

@ApiTags('Target Comments')
@Controller('target-comments')
export class TargetCommentsController {
  constructor(private readonly targetCommentsService: TargetCommentsService) {}

  @Get('/:targetId')
  @ApiQuery({ name: 'pageNumber', required: false, type: Number })
  @ApiQuery({ name: 'pageSize', required: false, type: Number })
  @ApiParam({ name: 'targetId', required: true, type: String })
  public async getTargetComments(
    @Param('targetId') targetId: string,
    @Query('pageNumber') pageNumber: number = 1,
    @Query('pageSize') pageSize: number = 10,
  ): Promise<{
    data: TargetComments[];
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  }> {
    return this.targetCommentsService.getTargetComments(
      targetId,
      pageNumber,
      pageSize,
    );
  }

  @Post('/:targetId')
  @ApiParam({ name: 'targetId', required: true, type: String })
  public async createTargetComment(
    @Param('targetId') targetId: string,
    @Body() body: CreateTargetCommentDto,
  ): Promise<TargetComments> {
    return this.targetCommentsService.createTargetComment(
      targetId,
      body.comment,
      body.createdBy,
    );
  }

  @Delete('/:messageId')
  @ApiParam({ name: 'messageId', required: true, type: String })
  public async deleteTargetComment(@Param('messageId') messageId: string) {
    return this.targetCommentsService.deleteTargetComment(messageId);
  }
}
