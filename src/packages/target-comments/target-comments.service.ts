import { Injectable } from '@nestjs/common';
import {
  TargetCommentsRepository,
  TargetComments,
} from '@ghq-abi/northstar-domain';

@Injectable()
export class TargetCommentsService {
  constructor(
    private readonly targetCommentsRepository: TargetCommentsRepository,
  ) {}

  async getTargetComments(
    targetId: string,
    pageNumber: number,
    pageSize: number,
  ): Promise<{
    data: TargetComments[];
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  }> {
    return this.targetCommentsRepository.findMessagesWithPagination(
      targetId,
      pageNumber,
      pageSize,
    );
  }

  async createTargetComment(
    targetId: string,
    comment: string,
    employeeId: string,
  ): Promise<TargetComments> {
    return await this.targetCommentsRepository.createMessage(
      targetId,
      comment,
      employeeId,
    );
  }

  async deleteTargetComment(messageId: string) {
    return this.targetCommentsRepository.deleteMessage(messageId);
  }
}
