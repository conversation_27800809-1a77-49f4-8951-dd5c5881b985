import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class CreateTargetCommentDto {
  @ApiProperty({
    description: 'Message',
    example: 'This is a comment message',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  comment: string;

  @ApiProperty({
    description: 'User GUID who created the comment',
    example: '6861F84A-AC95-4C1A-B30D-0D8A703B719C',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  createdBy: string; //TODO: Get who created from @User annotation
}
