import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TargetCommentsService } from './target-comments.service';
import { TargetCommentsController } from './target-comments.controller';
import {
  TargetCommentsEntity,
  TargetCommentsRepository,
} from '@ghq-abi/northstar-domain';
import { TargetTypeModule } from '../target-type/target-type.module';
import { TargetsModule } from '../targets/targets.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([TargetCommentsEntity]),
    TargetsModule,
    TargetTypeModule,
  ],
  controllers: [TargetCommentsController],
  providers: [TargetCommentsService, TargetCommentsRepository],
  exports: [
    TypeOrmModule.forFeature([TargetCommentsEntity]),
    TargetCommentsService,
  ],
})
export class TargetCommentsModule {}
