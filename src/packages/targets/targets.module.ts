import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TargetsService } from './targets.service';
import { TargetsController } from './targets.controller';
import { TargetRepository, TargetEntity } from '@ghq-abi/northstar-domain';
import { TargetTypeModule } from '../target-type/target-type.module';

@Module({
  imports: [TypeOrmModule.forFeature([TargetEntity]), TargetTypeModule],
  controllers: [TargetsController],
  providers: [TargetsService, TargetRepository],
  exports: [
    TypeOrmModule.forFeature([TargetEntity]),
    TargetsService,
    TargetRepository,
  ],
})
export class TargetsModule {}
