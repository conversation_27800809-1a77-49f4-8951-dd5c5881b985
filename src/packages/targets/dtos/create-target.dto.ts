import { TargetType } from '@ghq-abi/northstar-domain';
import { ApiProperty } from '@nestjs/swagger';

export class CreateTargetDto {
  @ApiProperty({
    description: 'Unique identifier for the target (optional)',
    example: 'A70BB61B-47DF-4A4B-9525-36118C76AD8A',
    required: false,
  })
  uid?: string;

  @ApiProperty({
    description: 'Weight value for the target',
    example: 0,
  })
  weight: number;

  @ApiProperty({
    description: 'Scope identifier for the target',
    example: 'scope1',
  })
  scope: string;

  @ApiProperty({
    description:
      'Unique identifier of the deliverable associated with this target (optional)',
    example: 'C1931740-5E3E-49F6-B7F0-C2F5FE4CA703',
    required: false,
  })
  uidDeliverable?: string;

  @ApiProperty({
    description: 'uid of the proposal associated with this target (optional)',
    example: '68C4B225-9D8D-4066-BC37-6E7F05DF232B',
    required: false,
  })
  uidProposal?: string;

  @ApiProperty({
    description: 'Type of the target',
    example: TargetType.PROPOSAL,
    required: false,
  })
  targetType?: TargetType;

  @ApiProperty({
    description: 'Child targets (optional)',
    type: [CreateTargetDto],
    required: false,
    example: [
      {
        weight: 0,
        scope: 'scope4',
        type: 'PROPOSAL',
        uidDeliverable: 'C1931740-5E3E-49F6-B7F0-C2F5FE4CA703',
      },
    ],
  })
  children?: CreateTargetDto[];
}
