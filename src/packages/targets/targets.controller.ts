import { Body, Controller, Get, Post } from '@nestjs/common';
import { TargetsService } from './targets.service';
import { ApiTags } from '@nestjs/swagger';
import { CreateTargetDto } from './dtos';

@ApiTags('Targets')
@Controller('targets')
export class TargetsController {
  constructor(private readonly targetsService: TargetsService) {}

  @Get()
  public async getTargets() {
    return this.targetsService.findAllTargets();
  }

  @Post()
  public async createTarget(@Body() target: CreateTargetDto) {
    return this.targetsService.createTarget(target);
  }
}
