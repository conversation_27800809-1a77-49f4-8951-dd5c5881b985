import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProposalCommentsService } from './proposal-comments.service';
import { ProposalCommentsController } from './proposal-comments.controller';
import {
  ProposalCommentsEntity,
  ProposalCommentsRepository,
} from '@ghq-abi/northstar-domain';
import { TargetTypeModule } from '../target-type/target-type.module';
import { ProposalModule } from '../proposal/proposal.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProposalCommentsEntity]),
    ProposalModule,
    TargetTypeModule,
  ],
  controllers: [ProposalCommentsController],
  providers: [ProposalCommentsService, ProposalCommentsRepository],
  exports: [
    TypeOrmModule.forFeature([ProposalCommentsEntity]),
    ProposalCommentsService,
  ],
})
export class ProposalCommentsModule {}
