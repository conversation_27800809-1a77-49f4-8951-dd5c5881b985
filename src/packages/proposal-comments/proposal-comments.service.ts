import { Injectable } from '@nestjs/common';
import {
  ProposalCommentsRepository,
  ProposalComments,
} from '@ghq-abi/northstar-domain';

@Injectable()
export class ProposalCommentsService {
  constructor(
    private readonly proposalCommentsRepository: ProposalCommentsRepository,
  ) {}

  async getProposalComments(
    proposalId: string,
    pageNumber: number,
    pageSize: number,
  ): Promise<{
    data: ProposalComments[];
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  }> {
    return this.proposalCommentsRepository.findMessagesWithPagination(
      proposalId,
      pageNumber,
      pageSize,
    );
  }

  async createProposalComment(
    proposalId: string,
    comment: string,
    employeeId: string,
  ): Promise<ProposalComments> {
    return await this.proposalCommentsRepository.createMessage(
      proposalId,
      comment,
      employeeId,
    );
  }

  async deleteProposalComment(messageId: string) {
    return this.proposalCommentsRepository.deleteMessage(messageId);
  }
}
