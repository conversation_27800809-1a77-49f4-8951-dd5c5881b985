import {
  Body,
  Controller,
  Get,
  Post,
  Param,
  Query,
  Delete,
} from '@nestjs/common';
import { ProposalCommentsService } from './proposal-comments.service';
import { ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { ProposalComments } from '@ghq-abi/northstar-domain';
import { CreateTargetCommentDto } from './dtos/create-comment.dto';

@ApiTags('Proposal Comments')
@Controller('proposal-comments')
export class ProposalCommentsController {
  constructor(
    private readonly proposalCommentsService: ProposalCommentsService,
  ) {}

  @Get('/:proposalId')
  @ApiQuery({ name: 'pageNumber', required: false, type: Number })
  @ApiQuery({ name: 'pageSize', required: false, type: Number })
  @ApiParam({ name: 'proposalId', required: true, type: String })
  public async getProposalComments(
    @Param('proposalId') proposalId: string,
    @Query('pageNumber') pageNumber: number = 1,
    @Query('pageSize') pageSize: number = 10,
  ): Promise<{
    data: ProposalComments[];
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  }> {
    return this.proposalCommentsService.getProposalComments(
      proposalId,
      pageNumber,
      pageSize,
    );
  }

  @Post('/:proposalId')
  @ApiParam({ name: 'proposalId', required: true, type: String })
  public async createProposalComment(
    @Param('proposalId') proposalId: string,
    @Body() body: CreateTargetCommentDto,
  ): Promise<ProposalComments> {
    return this.proposalCommentsService.createProposalComment(
      proposalId,
      body.comment,
      body.createdBy,
    );
  }

  @Delete('/:messageId')
  @ApiParam({ name: 'messageId', required: true, type: String })
  public async deleteProposalComment(@Param('messageId') messageId: string) {
    return this.proposalCommentsService.deleteProposalComment(messageId);
  }
}
