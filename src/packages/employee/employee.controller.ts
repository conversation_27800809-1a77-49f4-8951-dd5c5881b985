import { Controller, Get, Param, Query } from '@nestjs/common';
import { EmployeeService } from './employee.service';
import { ApiParam, ApiQuery } from '@nestjs/swagger';
import { ProposalEmployeeEntity } from '@ghq-abi/northstar-domain';

@Controller('employee')
export class EmployeeController {
  constructor(private readonly employeeService: EmployeeService) {}

  @Get(':employeeUuid/proposals')
  @ApiParam({
    name: 'employeeUuid',
    required: false,
    type: String,
  })
  @ApiQuery({ name: 'year', required: false, type: Number })
  findAllEmployeeProposals(
    @Param('employeeUuid') employeeUuid: string,
    @Query('year') year: number,
  ): Promise<ProposalEmployeeEntity> {
    return this.employeeService.findAllEmployeeProposals({
      employeeUuid,
      year,
    });
  }
}
