import {
  FindEmployeesProposalsFilters,
  ProposalEmployeeEntity,
  ProposalEmployeeRepository,
} from '@ghq-abi/northstar-domain';
import { Injectable } from '@nestjs/common';

@Injectable()
export class EmployeeService {
  constructor(
    private readonly proposalEmployeeRepository: ProposalEmployeeRepository,
  ) {}

  public async findAllEmployeeProposals(
    filters: FindEmployeesProposalsFilters,
  ): Promise<ProposalEmployeeEntity> {
    return this.proposalEmployeeRepository.findAllProposalEmployeeProposals(
      filters,
    );
  }
}
