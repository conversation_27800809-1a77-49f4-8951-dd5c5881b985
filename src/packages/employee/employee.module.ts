import { Module } from '@nestjs/common';
import { EmployeeService } from './employee.service';
import { EmployeeController } from './employee.controller';
import {
  ProposalEmployeeEntity,
  ProposalEmployeeRepository,
} from '@ghq-abi/northstar-domain';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([ProposalEmployeeEntity])],
  controllers: [EmployeeController],
  providers: [EmployeeService, ProposalEmployeeRepository],
  exports: [
    TypeOrmModule.forFeature([ProposalEmployeeEntity]),
    EmployeeService,
    ProposalEmployeeRepository,
  ],
})
export class EmployeeModule {}
