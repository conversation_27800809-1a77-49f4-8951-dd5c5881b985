import tracer from 'dd-trace';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import {
  // ForbiddenException,
  // UnauthorizedException,
  ValidationPipe,
} from '@nestjs/common';
import * as compress from 'compression';
import helmet from 'helmet';
import * as session from 'express-session';
import RedisStore from 'connect-redis';
// import * as cookieParser from 'cookie-parser';
import { redis } from './libs/redis';
// import { csrf } from './csrf';

process.env.DD_ENV &&
  tracer.init({
    service: 'tsc-catchball-api',
    logInjection: true,
    env: process.env.DD_ENV,
    runtimeMetrics: true,
    logLevel: 'debug',
    startupLogs: true,
    profiling: true,
    clientIpEnabled: true,
    reportHostname: true,
    plugins: true,
    tags: {
      'tsc-catchball-api': 'tsc-catchball-api',
    },
  });

const ROOT_PATH = 'api/catchball/';
const API_DOCS_URL = '/docs';
// const HEALTH_CHECK_URL = '/health-check';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
    }),
  );

  // cors for security
  app.enableCors({
    origin:
      process.env.APP_ENV === 'local'
        ? 'http://localhost:3000'
        : /.*.ab-inbev.com$/,
    credentials: true,
  });

  // helmet for security
  app.use(helmet());
  // gzip compression
  app.use(compress());

  if (process.env.SESSION_SECRET === undefined) {
    throw new Error('SESSION_SECRET environment variable is not set');
  }

  //CREATE SESSION
  app.use(
    session({
      secret: process.env.SESSION_SECRET,
      name: process.env.SESSION_NAME,
      resave: true,
      saveUninitialized: true,
      cookie: {
        secure: process.env.SESSION_SECURE === 'true',
        path: '/',
        httpOnly: true,
      },
      store: new RedisStore({
        client: redis,
      }),
    }),
  );

  //CHECK USER SESSION
  // app.use((req, res, next) => {
  // if (
  //   (req.session.user && req.session.user.uuid) ||
  //   req.url.endsWith(HEALTH_CHECK_URL) ||
  //   req.url.includes(API_DOCS_URL)
  // ) {
  // req.session = { user: { uid: '' } };
  // return next();
  // }

  // throw new UnauthorizedException('Invalid user session');
  // });

  // // CSRF protection
  // app.use(cookieParser(process.env.SESSION_SECRET));
  // app.use(csrf(process.env.CSURF_SECRET, [HEALTH_CHECK_URL]));
  // app.use((err, req, res, next) => {
  //   if (err.code !== 'EBADCSRFTOKEN') return next(err);
  //   throw new ForbiddenException('session has expired or tampered with');
  // });

  const config = new DocumentBuilder()
    .setTitle('Catchball API')
    .setDescription('The Catchball API')
    .setVersion('0.1')
    .addServer(`http://localhost:${process.env.PORT}/`, 'local')
    .addServer(`https://tsc-dev.ab-inbev.com/${ROOT_PATH}`, 'dev')
    .addServer(`https://tsc-qa.ab-inbev.com/${ROOT_PATH}`, 'qa')
    .addServer(`https://northstar.ab-inbev.com/${ROOT_PATH}`, 'prod')
    .build();
  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup(API_DOCS_URL, app, documentFactory);

  await app.listen(process.env.PORT);
}
bootstrap();
